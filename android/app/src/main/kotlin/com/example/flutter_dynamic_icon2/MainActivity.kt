package com.example.flutter_dynamic_icon2

import android.content.ComponentName
import android.content.pm.PackageManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.flutter_dynamic_icon2/icon_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "changeIcon" -> {
                    val iconName = call.argument<String>("iconName")
                    if (iconName != null) {
                        val success = changeAppIcon(iconName)
                        if (success) {
                            result.success("图标切换成功")
                        } else {
                            result.error("ICON_ERROR", "图标切换失败", null)
                        }
                    } else {
                        result.error("INVALID_ARGUMENT", "图标名称不能为空", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun changeAppIcon(iconName: String): Boolean {
        return try {
            val packageManager = packageManager
            val packageName = packageName

            // 定义所有图标别名
            val aliases = mapOf(
                // "icon_default" to "$packageName.DefaultIcon",
                "icon_red" to "$packageName.RedIcon",
                "icon_blue" to "$packageName.BlueIcon"
            )

            // 禁用所有别名
            aliases.values.forEach { aliasName ->
                val componentName = ComponentName(packageName, aliasName)
                packageManager.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                    PackageManager.DONT_KILL_APP
                )
            }

            // 启用目标别名
            val targetAlias = aliases[iconName]
            if (targetAlias != null) {
                val componentName = ComponentName(packageName, targetAlias)
                packageManager.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    PackageManager.DONT_KILL_APP
                )
                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
}
