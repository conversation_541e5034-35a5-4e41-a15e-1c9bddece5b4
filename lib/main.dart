import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  static const platform =
      MethodChannel('com.example.flutter_dynamic_icon2/icon_channel');
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(428, 926),
      builder: (context, child) {
        return MaterialApp(
          home: Scaffold(
            body: Center(
              child: SizedBox(
                width: 320.h,
                height: 320.h,
                child: PageView.builder(
                  itemCount: 3,
                  itemBuilder: (context, index) {
                    return Container(
                      color: Colors.purple,
                      child: Center(child: Text("$index")),
                    );
                  },
                ),
              ),
              // Column(
              //   mainAxisSize: MainAxisSize.min,
              //   children: [
              //     GestureDetector(
              //       onTap: () {
              //         _changeIcon('icon_red');
              //       },
              //       child: Text('Change 2'),
              //     ),
              //   ],
              // ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _changeIcon(String iconName) async {
    try {
      final String result = await platform.invokeMethod('changeIcon', {
        'iconName': iconName,
      });

      // setState(() {
      //   _currentIcon = iconName;
      //   _statusMessage = result;
      // });

      // 显示成功消息
      // if (mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(
      //       content: Text(result),
      //       backgroundColor: Colors.green,
      //       duration: const Duration(seconds: 2),
      //     ),
      //   );
      // }
    } on PlatformException catch (e) {
      // setState(() {
      //   _statusMessage = "图标切换失败: ${e.message}";
      // });

      // 显示错误消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("错误: ${e.message}"),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
